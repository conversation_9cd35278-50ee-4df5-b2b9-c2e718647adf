# Port Forwarding Tool

Bu Windows uygulaması, `http://localhost:61000` adresine gelen tüm HTTP isteklerini `https://zenzefi.mb-link.com:61000` adresine yönlendirir.

## Özellikler

- ✅ HTTP/HTTPS proxy sunucusu
- ✅ Basit Windows Forms arayüzü
- ✅ Gerçek zamanlı log görüntüleme
- ✅ Start/Stop kontrolü
- ✅ SSL sertifika doğrulamasını bypass etme
- ✅ Tüm HTTP metodlarını destekleme (GET, POST, PUT, DELETE, vb.)
- ✅ Request/Response header'larını kopyalama
- ✅ Request body'sini kopyalama

## Gereksinimler

- .NET 6.0 veya üzeri
- Windows işletim sistemi
- Yönetici hakları (port 61000'i dinlemek için)

## Kurulum ve Çalıştırma

1. Projeyi derleyin:
```bash
dotnet build
```

2. Uygulamayı çalıştırın:
```bash
dotnet run
```

Veya Visual Studio ile açıp F5 ile çalıştırabilirsiniz.

## Kullanım

1. Uygulamayı yönetici olarak çalıştırın
2. "Start Forwarding" butonuna tıklayın
3. Artık `http://localhost:61000` adresine yapılan istekler `https://zenzefi.mb-link.com:61000` adresine yönlendirilecek
4. Log penceresinden istekleri takip edebilirsiniz
5. "Stop Forwarding" ile durdurabilirsiniz

## Teknik Detaylar

- **HttpListener** kullanarak localhost:61000'i dinler
- **HttpClient** kullanarak hedef sunucuya istekleri iletir
- SSL sertifika doğrulaması devre dışı bırakılmıştır (test amaçlı)
- Asenkron işlemler kullanılarak performans optimize edilmiştir
- Tüm HTTP header'ları ve body içeriği korunur

## Güvenlik Notu

Bu uygulama SSL sertifika doğrulamasını bypass eder. Üretim ortamında kullanmadan önce güvenlik ayarlarını gözden geçirin.
