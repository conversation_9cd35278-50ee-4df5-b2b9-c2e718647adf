using System;
using System.Drawing;
using System.Windows.Forms;

namespace PortForwarding
{
    public partial class MainForm : Form
    {
        private ProxyServer? _proxyServer;
        private Button _startStopButton;
        private TextBox _logTextBox;
        private Label _statusLabel;
        private Label _sourceLabel;
        private Label _targetLabel;

        public MainForm()
        {
            InitializeComponent();
            InitializeProxyServer();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "Port Forwarding Tool";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;

            // Source label
            _sourceLabel = new Label
            {
                Text = "Source: http://localhost:61000",
                Location = new Point(20, 20),
                Size = new Size(300, 23),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            this.Controls.Add(_sourceLabel);

            // Target label
            _targetLabel = new Label
            {
                Text = "Target: https://zenzefi.mb-link.com:61000",
                Location = new Point(20, 50),
                Size = new Size(400, 23),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            this.Controls.Add(_targetLabel);

            // Status label
            _statusLabel = new Label
            {
                Text = "Status: Stopped",
                Location = new Point(20, 90),
                Size = new Size(200, 23),
                ForeColor = Color.Red,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            this.Controls.Add(_statusLabel);

            // Start/Stop button
            _startStopButton = new Button
            {
                Text = "Start Forwarding",
                Location = new Point(20, 120),
                Size = new Size(150, 35),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            _startStopButton.Click += StartStopButton_Click;
            this.Controls.Add(_startStopButton);

            // Clear log button
            var clearButton = new Button
            {
                Text = "Clear Log",
                Location = new Point(180, 120),
                Size = new Size(100, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };
            clearButton.Click += (s, e) => _logTextBox.Clear();
            this.Controls.Add(clearButton);

            // Log textbox
            _logTextBox = new TextBox
            {
                Location = new Point(20, 170),
                Size = new Size(540, 280),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                BackColor = Color.Black,
                ForeColor = Color.Lime,
                Font = new Font("Consolas", 8F)
            };
            this.Controls.Add(_logTextBox);

            // Instructions label
            var instructionsLabel = new Label
            {
                Text = "Bu uygulama localhost:61000'e gelen tüm HTTP isteklerini https://zenzefi.mb-link.com:61000'e yönlendirir.",
                Location = new Point(300, 90),
                Size = new Size(260, 60),
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.DarkBlue
            };
            this.Controls.Add(instructionsLabel);

            this.ResumeLayout(false);
        }

        private void InitializeProxyServer()
        {
            _proxyServer = new ProxyServer();
            _proxyServer.LogMessage += OnLogMessage;
        }

        private void OnLogMessage(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(OnLogMessage), message);
                return;
            }

            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            _logTextBox.AppendText($"[{timestamp}] {message}\r\n");
            _logTextBox.SelectionStart = _logTextBox.Text.Length;
            _logTextBox.ScrollToCaret();
        }

        private async void StartStopButton_Click(object sender, EventArgs e)
        {
            if (_proxyServer == null) return;

            try
            {
                _startStopButton.Enabled = false;

                if (_proxyServer.IsRunning)
                {
                    _proxyServer.Stop();
                    UpdateUI(false);
                }
                else
                {
                    await _proxyServer.StartAsync();
                    UpdateUI(_proxyServer.IsRunning);
                }
            }
            catch (Exception ex)
            {
                OnLogMessage($"Error: {ex.Message}");
                UpdateUI(false);
            }
            finally
            {
                _startStopButton.Enabled = true;
            }
        }

        private void UpdateUI(bool isRunning)
        {
            if (isRunning)
            {
                _startStopButton.Text = "Stop Forwarding";
                _startStopButton.BackColor = Color.Red;
                _statusLabel.Text = "Status: Running";
                _statusLabel.ForeColor = Color.Green;
            }
            else
            {
                _startStopButton.Text = "Start Forwarding";
                _startStopButton.BackColor = Color.Green;
                _statusLabel.Text = "Status: Stopped";
                _statusLabel.ForeColor = Color.Red;
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _proxyServer?.Dispose();
            base.OnFormClosing(e);
        }
    }
}
