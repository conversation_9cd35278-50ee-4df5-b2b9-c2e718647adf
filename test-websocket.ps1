# WebSocket Test Script
Write-Host "WebSocket Proxy Test Script" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green
Write-Host ""

# Test WebSocket connection through proxy
Write-Host "Testing WebSocket connection through proxy..." -ForegroundColor Yellow

try {
    # Create WebSocket client
    $ws = New-Object System.Net.WebSockets.ClientWebSocket
    $uri = [System.Uri]::new("ws://localhost:61000/")
    
    Write-Host "Connecting to: $uri" -ForegroundColor Cyan
    
    # Connect to WebSocket through proxy
    $connectTask = $ws.ConnectAsync($uri, [System.Threading.CancellationToken]::None)

    Write-Host "Attempting connection..." -ForegroundColor Yellow
    if (-not $connectTask.Wait(10000)) { # 10 second timeout
        Write-Host "❌ Connection timeout!" -ForegroundColor Red
        return
    }

    if ($connectTask.IsFaulted) {
        Write-Host "❌ Connection failed!" -ForegroundColor Red
        Write-Host "Exception: $($connectTask.Exception.InnerException.Message)" -ForegroundColor Red
        return
    }
    
    if ($ws.State -eq [System.Net.WebSockets.WebSocketState]::Open) {
        Write-Host "✅ WebSocket connection successful!" -ForegroundColor Green
        Write-Host "Connection State: $($ws.State)" -ForegroundColor Cyan
        
        # Send a test message
        $message = "Hello from PowerShell WebSocket test!"
        $bytes = [System.Text.Encoding]::UTF8.GetBytes($message)
        $buffer = [System.ArraySegment[byte]]::new($bytes)
        
        Write-Host "Sending test message: '$message'" -ForegroundColor Yellow
        $sendTask = $ws.SendAsync($buffer, [System.Net.WebSockets.WebSocketMessageType]::Text, $true, [System.Threading.CancellationToken]::None)
        $sendTask.Wait(2000)
        
        Write-Host "✅ Message sent successfully!" -ForegroundColor Green
        
        # Try to receive a response (with timeout)
        $receiveBuffer = New-Object byte[] 1024
        $receiveSegment = [System.ArraySegment[byte]]::new($receiveBuffer)
        
        Write-Host "Waiting for response..." -ForegroundColor Yellow
        $receiveTask = $ws.ReceiveAsync($receiveSegment, [System.Threading.CancellationToken]::None)
        
        if ($receiveTask.Wait(3000)) {
            $result = $receiveTask.Result
            $receivedMessage = [System.Text.Encoding]::UTF8.GetString($receiveBuffer, 0, $result.Count)
            Write-Host "✅ Received response: '$receivedMessage'" -ForegroundColor Green
            Write-Host "Message Type: $($result.MessageType)" -ForegroundColor Cyan
            Write-Host "End of Message: $($result.EndOfMessage)" -ForegroundColor Cyan
        } else {
            Write-Host "⚠️ No response received within timeout" -ForegroundColor Yellow
        }
        
        # Close connection
        $closeTask = $ws.CloseAsync([System.Net.WebSockets.WebSocketCloseStatus]::NormalClosure, "Test completed", [System.Threading.CancellationToken]::None)
        $closeTask.Wait(2000)
        Write-Host "✅ WebSocket connection closed gracefully" -ForegroundColor Green
        
    } else {
        Write-Host "❌ WebSocket connection failed!" -ForegroundColor Red
        Write-Host "Connection State: $($ws.State)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ WebSocket test failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Make sure:" -ForegroundColor Yellow
    Write-Host "1. The proxy application is running" -ForegroundColor Yellow
    Write-Host "2. You clicked 'Start Forwarding' button" -ForegroundColor Yellow
    Write-Host "3. The target WebSocket server is available" -ForegroundColor Yellow
} finally {
    if ($ws) {
        $ws.Dispose()
    }
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
