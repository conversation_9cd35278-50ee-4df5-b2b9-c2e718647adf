{"format": 1, "restore": {"D:\\Codes\\VisualC\\Mercedes\\PortForwarding\\PortForwarding.csproj": {}}, "projects": {"D:\\Codes\\VisualC\\Mercedes\\PortForwarding\\PortForwarding.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Codes\\VisualC\\Mercedes\\PortForwarding\\PortForwarding.csproj", "projectName": "PortForwarding", "projectPath": "D:\\Codes\\VisualC\\Mercedes\\PortForwarding\\PortForwarding.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Codes\\VisualC\\Mercedes\\PortForwarding\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WinForms.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Program Files (x86)\\Syncfusion\\Essential Studio\\WinUI\\26.1.35\\NuGetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "System.Net.WebSockets": {"target": "Package", "version": "[4.3.0, )"}, "System.Net.WebSockets.Client": {"target": "Package", "version": "[4.3.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200\\RuntimeIdentifierGraph.json"}}}}}