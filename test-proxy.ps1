# Port Forwarding Test Script
Write-Host "Port Forwarding Test Script" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""

# Test if the proxy is running
Write-Host "Testing proxy connection..." -ForegroundColor Yellow

try {
    # HTTPS için SSL sertifika doğrulamasını bypass et
    [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}
    $response = Invoke-WebRequest -Uri "https://localhost:61000/" -Method GET -TimeoutSec 10
    Write-Host "✅ Proxy is running!" -ForegroundColor Green
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Cyan
    Write-Host "Response Length: $($response.Content.Length) bytes" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Response Headers:" -ForegroundColor Cyan
    $response.Headers | Format-Table -AutoSize
    Write-Host ""
    Write-Host "First 500 characters of response:" -ForegroundColor Cyan
    Write-Host $response.Content.Substring(0, [Math]::Min(500, $response.Content.Length)) -ForegroundColor White
}
catch {
    Write-Host "❌ Proxy connection failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Make sure:" -ForegroundColor Yellow
    Write-Host "1. The Windows Forms application is running" -ForegroundColor Yellow
    Write-Host "2. You clicked 'Start Forwarding' button" -ForegroundColor Yellow
    Write-Host "3. The application is running as Administrator" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
