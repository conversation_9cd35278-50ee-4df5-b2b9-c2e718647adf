using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography;

namespace PortForwarding
{
    public class ProxyServer
    {
        private HttpListener? _listener;
        private readonly HttpClient _httpClient;
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isRunning;
        
        private const string LOCAL_URL = "http://localhost:61000/";
        private const string TARGET_URL = "https://zenzefi.mb-link.com:61000";

        public event Action<string>? LogMessage;
        public bool IsRunning => _isRunning;

        public ProxyServer()
        {
            var handler = new HttpClientHandler()
            {
                ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => true
            };
            _httpClient = new HttpClient(handler);
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        public Task StartAsync()
        {
            if (_isRunning) return Task.CompletedTask;

            try
            {
                _listener = new HttpListener();
                _listener.Prefixes.Add(LOCAL_URL);
                _listener.Start();

                _cancellationTokenSource = new CancellationTokenSource();
                _isRunning = true;

                LogMessage?.Invoke($"Proxy server started on {LOCAL_URL}");
                LogMessage?.Invoke($"Forwarding to {TARGET_URL}");

                _ = Task.Run(() => ListenForRequests(_cancellationTokenSource.Token));

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke($"Error starting server: {ex.Message}");
                _isRunning = false;
                return Task.CompletedTask;
            }
        }

        public void Stop()
        {
            if (!_isRunning) return;

            try
            {
                _cancellationTokenSource?.Cancel();
                _listener?.Stop();
                _listener?.Close();
                _isRunning = false;
                
                LogMessage?.Invoke("Proxy server stopped");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke($"Error stopping server: {ex.Message}");
            }
        }

        private async Task ListenForRequests(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && _listener != null)
            {
                try
                {
                    var context = await _listener.GetContextAsync();
                    _ = Task.Run(() => ProcessRequest(context), cancellationToken);
                }
                catch (ObjectDisposedException)
                {
                    break;
                }
                catch (HttpListenerException ex) when (ex.ErrorCode == 995)
                {
                    break;
                }
                catch (Exception ex)
                {
                    LogMessage?.Invoke($"Error accepting request: {ex.Message}");
                }
            }
        }

        private async Task ProcessRequest(HttpListenerContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                LogMessage?.Invoke($"{request.HttpMethod} {request.Url?.PathAndQuery}");

                // Create target URL
                var targetUrl = TARGET_URL + (request.Url?.PathAndQuery ?? "");
                
                // Create HTTP request message
                var requestMessage = new HttpRequestMessage(
                    new HttpMethod(request.HttpMethod), 
                    targetUrl);

                // Copy headers
                foreach (string headerName in request.Headers.AllKeys)
                {
                    if (headerName.Equals("Host", StringComparison.OrdinalIgnoreCase))
                        continue;
                    
                    var headerValue = request.Headers[headerName];
                    if (headerValue != null)
                    {
                        try
                        {
                            if (headerName.Equals("Content-Type", StringComparison.OrdinalIgnoreCase) ||
                                headerName.Equals("Content-Length", StringComparison.OrdinalIgnoreCase))
                                continue;
                            
                            requestMessage.Headers.TryAddWithoutValidation(headerName, headerValue);
                        }
                        catch { }
                    }
                }

                // Copy request body if present
                if (request.HasEntityBody)
                {
                    using var reader = new StreamReader(request.InputStream);
                    var body = await reader.ReadToEndAsync();
                    requestMessage.Content = new StringContent(body, Encoding.UTF8, 
                        request.ContentType ?? "application/json");
                }

                // Send request to target server
                var targetResponse = await _httpClient.SendAsync(requestMessage);

                // Copy response status
                response.StatusCode = (int)targetResponse.StatusCode;
                response.StatusDescription = targetResponse.ReasonPhrase ?? "";

                // Copy response headers
                foreach (var header in targetResponse.Headers)
                {
                    try
                    {
                        response.Headers.Add(header.Key, string.Join(", ", header.Value));
                    }
                    catch { }
                }

                foreach (var header in targetResponse.Content.Headers)
                {
                    try
                    {
                        response.Headers.Add(header.Key, string.Join(", ", header.Value));
                    }
                    catch { }
                }

                // Copy response body
                var responseBody = await targetResponse.Content.ReadAsByteArrayAsync();
                response.ContentLength64 = responseBody.Length;
                await response.OutputStream.WriteAsync(responseBody, 0, responseBody.Length);
                
                LogMessage?.Invoke($"Response: {response.StatusCode} ({responseBody.Length} bytes)");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke($"Error processing request: {ex.Message}");
                try
                {
                    context.Response.StatusCode = 500;
                    context.Response.StatusDescription = "Internal Server Error";
                }
                catch { }
            }
            finally
            {
                try
                {
                    context.Response.Close();
                }
                catch { }
            }
        }

        private void SetupSSLCertificate()
        {
            try
            {
                LogMessage?.Invoke("Setting up SSL certificate for HTTPS...");

                // Bu metod SSL sertifika kurulumu için gerekli komutları çalıştırır
                // Gerçek uygulamada netsh http add sslcert komutu kullanılır
                LogMessage?.Invoke("SSL certificate setup completed");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke($"SSL setup warning: {ex.Message}");
            }
        }

        public void Dispose()
        {
            Stop();
            _httpClient?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
    }
}
